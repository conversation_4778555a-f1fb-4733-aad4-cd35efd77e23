# CIG Catalog Module - Krok 3: Repository Pattern a Datová Vrstva

## Přehled implementace

Krok 3 byl ú<PERSON><PERSON>šně dokončen s implementací kompletního Repository pattern pro čistou separaci datové vrstvy s podporou multijazyčnosti.

## Implementované komponenty

### 1. BaseRepository.php
Abstraktní základní třída poskytující společnou funkcionalitě pro všechny repository:

**Klíčové funkce:**
- Database connection management
- Error handling a structured logging
- Transaction support (BEGIN, COMMIT, ROLLBACK)
- Query execution s retry mechanismem
- Validace a sanitizace dat
- Cache mechanismus

**Metody:**
- `executeQuery()` - Bezpečné spouštění SELECT dotazů
- `executeValue()` - Získání jedné hodnoty z databáze
- `executeUpdate()` - Spouštění INSERT/UPDATE/DELETE dotazů
- `beginTransaction()`, `commit()`, `rollback()` - Spr<PERSON>va transakcí
- `executeWithRetry()` - Retry mechanismus s exponential backoff
- `validateInt()`, `validateString()` - Validace vstupních dat

### 2. CatalogRepository.php
Repository pro správu katalogů s multijazyčnou podporou:

**Základní CRUD operace:**
- `findById(int $id, int $langId): ?array` - Najít katalog podle ID
- `findAll(int $langId): array` - Všechny katalogy
- `findAllActive(int $langId): array` - Pouze aktivní katalogy
- `create(array $data): int` - Vytvoření nového katalogu
- `update(int $id, array $data): bool` - Aktualizace katalogu
- `delete(int $id): bool` - Smazání katalogu

**Pokročilé dotazy:**
- `findByPosition(int $langId): array` - Řazení podle pozice
- `findNewCatalogs(int $langId): array` - Pouze nové katalogy
- `findWithPagination(int $page, int $limit, int $langId): array` - Stránkování
- `search(string $query, int $langId): array` - Fulltextové vyhledávání
- `findActiveWithFilters(array $filters, int $langId): array` - Pokročilé filtrování

**Pozice management:**
- `updatePositions(array $positions): bool` - Hromadná aktualizace pozic
- `getMaxPosition(): int` - Získání nejvyšší pozice
- `reorderAfterDelete(int $deletedPosition): bool` - Přeřazení po smazání

**Statistiky a utility:**
- `incrementDownloadCount(int $id): bool` - Zvýšení počtu stažení
- `getStatistics(): array` - Statistiky katalogů

### 3. CatalogOrderRepository.php
Repository pro správu objednávek katalogů:

**Objednávky management:**
- `create(array $orderData): int` - Vytvoření objednávky
- `findByCatalogId(int $catalogId): array` - Objednávky podle katalogu
- `findByEmail(string $email): array` - Objednávky podle emailu
- `findRecent(int $days = 30): array` - Nedávné objednávky
- `updateStatus(int $orderId, string $status, string $adminNote = ''): bool` - Změna stavu

**Export a reporting:**
- `exportToCSV(array $filters): string` - Export do CSV
- `getOrdersByDateRange(DateTime $from, DateTime $to): array` - Objednávky v rozmezí
- `getStatistics(): array` - Statistiky objednávek

### 4. CatalogConfigRepository.php
Repository pro správu konfigurace s cache mechanismem:

**Konfigurace management:**
- `get(string $name, mixed $default = null): mixed` - Získání hodnoty
- `set(string $name, mixed $value): bool` - Nastavení hodnoty
- `getAll(): array` - Všechny konfigurace
- `delete(string $name): bool` - Smazání konfigurace

**Cache mechanismus:**
- `getWithCache(string $name): mixed` - Získání s cache
- `clearCache(): void` - Vymazání cache
- `warmupCache(): void` - Předehřátí cache
- `setCacheEnabled(bool $enabled): void` - Zapnutí/vypnutí cache

**Pokročilé funkce:**
- `getByPrefix(string $prefix): array` - Konfigurace podle prefixu
- `setMultiple(array $configs): bool` - Hromadné nastavení
- `initializeDefaults(): bool` - Inicializace výchozích hodnot
- `validateValue(string $key, mixed $value): bool` - Validace hodnot

## Technické vlastnosti

### Performance optimalizace
- **Prepared statements** - Ochrana proti SQL injection
- **Index využití** - Optimalizované dotazy s využitím databázových indexů
- **Lazy loading** - Načítání dat pouze při potřebě
- **Connection pooling** - Efektivní využití databázových spojení
- **Query caching** - Cache pro často používané dotazy

### Error handling
- **Structured logging** - Detailní logování s kontextem
- **Exception wrapping** - Konzistentní error handling
- **Graceful degradation** - Pokračování při dílčích chybách
- **Retry mechanismus** - Automatické opakování při dočasných chybách

### Multijazyčnost
- Kompletní podpora multijazyčných dat
- Automatické JOINy s jazykovými tabulkami
- Fallback mechanismus pro chybějící překlady
- Optimalizované dotazy pro různé jazyky

### Transaction support
- Automatická správa transakcí pro složité operace
- Rollback při chybách
- Nested transaction podpora
- Deadlock detection a recovery

## Testování

### Unit testy
Implementovány kompletní unit testy pro:
- `BaseRepositoryTest.php` - Test základní funkcionality
- `CatalogRepositoryTest.php` - Test katalogových operací

**Test coverage:**
- Úspěšné scénáře
- Error handling
- Edge cases
- Transaction management
- Validation logic

### Spuštění testů
```bash
# Spuštění všech repository testů
vendor/bin/phpunit modules/cig_catalog/tests/Repository/

# Spuštění konkrétního testu
vendor/bin/phpunit modules/cig_catalog/tests/Repository/CatalogRepositoryTest.php
```

## Použití

### Příklad použití CatalogRepository
```php
use CigCatalog\Repository\CatalogRepository;

$catalogRepo = new CatalogRepository();

// Získání aktivních katalogů
$catalogs = $catalogRepo->findAllActive($langId);

// Vytvoření nového katalogu
$catalogData = [
    'active' => 1,
    'position' => 1,
    'is_new' => 1,
    'lang' => [
        1 => [
            'name' => 'Nový katalog',
            'description' => 'Popis katalogu',
            'slug' => 'novy-katalog'
        ]
    ]
];
$catalogId = $catalogRepo->create($catalogData);

// Vyhledávání
$results = $catalogRepo->search('hledaný text', $langId);

// Stránkování
$paginated = $catalogRepo->findWithPagination(1, 10, $langId);
```

### Příklad použití CatalogConfigRepository
```php
use CigCatalog\Repository\CatalogConfigRepository;

$configRepo = new CatalogConfigRepository();

// Získání konfigurace
$itemsPerPage = $configRepo->get('items_per_page', 12);

// Nastavení konfigurace
$configRepo->set('admin_email', '<EMAIL>');

// Hromadné nastavení
$configRepo->setMultiple([
    'smtp_enabled' => true,
    'smtp_host' => 'smtp.example.com',
    'smtp_port' => 587
]);
```

## Bezpečnost

### SQL Injection Protection
- Všechny dotazy používají prepared statements
- Vstupní data jsou validována a sanitizována
- Escape funkce pro dynamické dotazy

### Data Validation
- Type-safe validace všech vstupů
- Whitelist approach pro povolené hodnoty
- Automatická sanitizace řetězců

### Access Control
- Repository pattern poskytuje kontrolovaný přístup k datům
- Logování všech databázových operací
- Error handling bez odhalení citlivých informací

## Výkon a škálovatelnost

### Database Optimization
- Optimalizované dotazy s minimálním počtem JOINů
- Využití indexů pro rychlé vyhledávání
- Batch operace pro hromadné změny

### Memory Management
- Lazy loading pro velké datasety
- Streaming pro export velkých objemů dat
- Garbage collection friendly implementace

### Caching Strategy
- Multi-level cache (memory + database)
- TTL based expiration
- Cache invalidation při změnách

## Monitoring a Debugging

### Logging
- Všechny databázové operace jsou logovány
- Performance metriky
- Error tracking s full stack trace

### Debug Mode
- Detailní SQL query logging
- Execution time tracking
- Memory usage monitoring

## Závěr

Krok 3 úspěšně implementoval robustní Repository pattern s následujícími výhodami:

1. **Čistá architektura** - Separace datové vrstvy od business logiky
2. **Testovatelnost** - Mock-friendly interface pro unit testy
3. **Maintainability** - Konzistentní API napříč všemi repository
4. **Performance** - Optimalizované dotazy a caching
5. **Bezpečnost** - SQL injection protection a data validation
6. **Škálovatelnost** - Připraveno pro růst aplikace

Repository vrstva je nyní připravena pro implementaci Service vrstvy v kroku 4.
