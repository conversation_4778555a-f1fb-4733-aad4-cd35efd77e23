<?php
/**
 * CIG Catalog Module
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

declare(strict_types=1);

namespace CigCatalog\Entity;

use ObjectModel;
use Db;
use DbQuery;
use Validate;
use Tools;

/**
 * CatalogConfig entity class
 * 
 * Manages configuration values for the catalog module
 */
class CatalogConfig extends ObjectModel
{
    /** @var int */
    public $id_config;

    /** @var string */
    public $config_key;

    /** @var string|null */
    public $config_value;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /** @var array Cache for configuration values */
    private static $cache = [];

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'cig_catalog_config',
        'primary' => 'id_config',
        'fields' => [
            'config_key' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isConfigName',
                'required' => true,
                'size' => 100
            ],
            'config_value' => [
                'type' => self::TYPE_STRING,
                'validate' => 'isAnything',
                'required' => false
            ],
            'date_add' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ],
            'date_upd' => [
                'type' => self::TYPE_DATE,
                'validate' => 'isDate',
                'required' => false
            ]
        ]
    ];

    /**
     * Constructor
     *
     * @param int|null $id
     */
    public function __construct($id = null)
    {
        parent::__construct($id);
    }

    /**
     * Get configuration value by key
     *
     * @param string $key
     * @param mixed $default_value
     * @return mixed
     */
    public static function get($key, $default_value = null)
    {
        // Check cache first
        if (isset(self::$cache[$key])) {
            return self::$cache[$key];
        }

        $sql = new DbQuery();
        $sql->select('config_value');
        $sql->from('cig_catalog_config');
        $sql->where('config_key = "' . pSQL($key) . '"');

        $value = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if ($value !== false) {
            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            // Cache the value
            self::$cache[$key] = $value;
            return $value;
        }

        return $default_value;
    }

    /**
     * Set configuration value
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function set($key, $value)
    {
        // Serialize complex values
        if (is_array($value) || is_object($value)) {
            $value = serialize($value);
        }

        // Check if key exists
        $sql = new DbQuery();
        $sql->select('id_config');
        $sql->from('cig_catalog_config');
        $sql->where('config_key = "' . pSQL($key) . '"');

        $id_config = Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($sql);

        if ($id_config) {
            // Update existing
            $result = Db::getInstance()->update(
                'cig_catalog_config',
                [
                    'config_value' => pSQL($value),
                    'date_upd' => date('Y-m-d H:i:s')
                ],
                'id_config = ' . (int) $id_config
            );
        } else {
            // Insert new
            $result = Db::getInstance()->insert(
                'cig_catalog_config',
                [
                    'config_key' => pSQL($key),
                    'config_value' => pSQL($value),
                    'date_add' => date('Y-m-d H:i:s'),
                    'date_upd' => date('Y-m-d H:i:s')
                ]
            );
        }

        if ($result) {
            // Update cache
            self::$cache[$key] = $value;
        }

        return $result;
    }

    /**
     * Delete configuration value
     *
     * @param string $key
     * @return bool
     */
    public static function deleteKey($key)
    {
        $result = Db::getInstance()->delete(
            'cig_catalog_config',
            'config_key = "' . pSQL($key) . '"'
        );

        if ($result) {
            // Remove from cache
            unset(self::$cache[$key]);
        }

        return $result;
    }

    /**
     * Get all configuration values
     *
     * @return array
     */
    public static function getAll()
    {
        $sql = new DbQuery();
        $sql->select('config_key, config_value');
        $sql->from('cig_catalog_config');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        $config = [];

        foreach ($results as $row) {
            $value = $row['config_value'];

            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            $config[$row['config_key']] = $value;
        }

        return $config;
    }

    /**
     * Get configuration values by prefix
     *
     * @param string $prefix
     * @return array
     */
    public static function getByPrefix($prefix)
    {
        $sql = new DbQuery();
        $sql->select('config_key, config_value');
        $sql->from('cig_catalog_config');
        $sql->where('config_key LIKE "' . pSQL($prefix) . '%"');

        $results = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
        $config = [];

        foreach ($results as $row) {
            $value = $row['config_value'];

            // Try to unserialize if it's a serialized value
            $unserialized = @unserialize($value);
            if ($unserialized !== false || $value === 'b:0;') {
                $value = $unserialized;
            }

            $config[$row['config_key']] = $value;
        }

        return $config;
    }

    /**
     * Clear configuration cache
     */
    public static function clearCache()
    {
        self::$cache = [];
    }

    /**
     * Get default configuration values
     *
     * @return array
     */
    public static function getDefaultValues()
    {
        return [
            'items_per_page' => 12,
            'enable_ordering' => true,
            'admin_email' => '',
            'from_name' => 'CIG Catalog System',
            'from_email' => '',
            'smtp_enabled' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls',
            'max_file_size' => 10485760, // 10MB
            'allowed_image_types' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'allowed_file_types' => ['pdf', 'zip', 'doc', 'docx', 'xls', 'xlsx'],
            'enable_download_tracking' => true,
            'enable_new_badge' => true,
            'new_badge_days' => 30,
            'enable_pagination' => true,
            'enable_search' => true,
            'cache_enabled' => true,
            'cache_ttl' => 3600
        ];
    }

    /**
     * Initialize default configuration values
     *
     * @return bool
     */
    public static function initializeDefaults()
    {
        $defaults = self::getDefaultValues();
        $success = true;

        foreach ($defaults as $key => $value) {
            // Only set if not already exists
            if (self::get($key) === null) {
                $success = self::set($key, $value) && $success;
            }
        }

        return $success;
    }

    /**
     * Validate configuration value
     *
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public static function validateValue($key, $value)
    {
        switch ($key) {
            case 'items_per_page':
            case 'max_file_size':
            case 'new_badge_days':
            case 'smtp_port':
            case 'cache_ttl':
                return is_numeric($value) && $value > 0;

            case 'enable_ordering':
            case 'smtp_enabled':
            case 'enable_download_tracking':
            case 'enable_new_badge':
            case 'enable_pagination':
            case 'enable_search':
            case 'cache_enabled':
                return is_bool($value) || in_array($value, ['0', '1', 0, 1, true, false]);

            case 'admin_email':
            case 'from_email':
                return empty($value) || Validate::isEmail($value);

            case 'smtp_encryption':
                return in_array($value, ['', 'ssl', 'tls']);

            case 'allowed_image_types':
            case 'allowed_file_types':
                return is_array($value) || is_string($value);

            default:
                return true;
        }
    }

    /**
     * Get formatted file size
     *
     * @param int $bytes
     * @return string
     */
    public static function formatFileSize($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
