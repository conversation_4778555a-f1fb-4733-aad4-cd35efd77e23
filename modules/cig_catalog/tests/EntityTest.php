<?php
/**
 * CIG Catalog Module - Entity Tests
 * 
 * <AUTHOR> Team
 * @copyright 2024 CIG
 * @license   Commercial License
 */

// Include PrestaShop bootstrap for testing
require_once dirname(__FILE__) . '/../../../../config/config.inc.php';

// Include module entities
require_once dirname(__FILE__) . '/../src/Entity/Catalog.php';
require_once dirname(__FILE__) . '/../src/Entity/CatalogOrder.php';
require_once dirname(__FILE__) . '/../src/Entity/CatalogConfig.php';
require_once dirname(__FILE__) . '/../src/Validator/CzechValidator.php';

use CigCatalog\Entity\Catalog;
use CigCatalog\Entity\CatalogOrder;
use CigCatalog\Entity\CatalogConfig;
use CigCatalog\Validator\CzechValidator;

/**
 * Simple test class for entity functionality
 */
class EntityTest
{
    private $errors = [];
    private $successes = [];

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "=== CIG Catalog Entity Tests ===\n\n";

        $this->testCatalogEntity();
        $this->testCatalogOrderEntity();
        $this->testCatalogConfigEntity();
        $this->testCzechValidator();

        $this->printResults();
    }

    /**
     * Test Catalog entity
     */
    private function testCatalogEntity()
    {
        echo "Testing Catalog entity...\n";

        try {
            // Test entity creation
            $catalog = new Catalog();
            $this->assert($catalog instanceof Catalog, "Catalog entity creation");

            // Test definition
            $this->assert(isset(Catalog::$definition), "Catalog definition exists");
            $this->assert(Catalog::$definition['table'] === 'cig_catalog', "Catalog table name");
            $this->assert(Catalog::$definition['primary'] === 'id_catalog', "Catalog primary key");
            $this->assert(Catalog::$definition['multilang'] === true, "Catalog multilang support");

            // Test field definitions
            $fields = Catalog::$definition['fields'];
            $this->assert(isset($fields['active']), "Active field defined");
            $this->assert(isset($fields['position']), "Position field defined");
            $this->assert(isset($fields['name']), "Name field defined");
            $this->assert($fields['name']['lang'] === true, "Name field is multilingual");

            // Test methods
            $this->assert(method_exists($catalog, 'getHighestPosition'), "getHighestPosition method exists");
            $this->assert(method_exists($catalog, 'generateSlug'), "generateSlug method exists");
            $this->assert(method_exists($catalog, 'getActiveCatalogs'), "getActiveCatalogs static method exists");

            echo "✓ Catalog entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "Catalog entity test failed: " . $e->getMessage();
            echo "✗ Catalog entity tests failed\n";
        }
    }

    /**
     * Test CatalogOrder entity
     */
    private function testCatalogOrderEntity()
    {
        echo "Testing CatalogOrder entity...\n";

        try {
            // Test entity creation
            $order = new CatalogOrder();
            $this->assert($order instanceof CatalogOrder, "CatalogOrder entity creation");

            // Test definition
            $this->assert(isset(CatalogOrder::$definition), "CatalogOrder definition exists");
            $this->assert(CatalogOrder::$definition['table'] === 'cig_catalog_order', "CatalogOrder table name");
            $this->assert(CatalogOrder::$definition['primary'] === 'id_order', "CatalogOrder primary key");

            // Test field definitions
            $fields = CatalogOrder::$definition['fields'];
            $this->assert(isset($fields['customer_email']), "Customer email field defined");
            $this->assert(isset($fields['status']), "Status field defined");
            $this->assert(isset($fields['status']['values']), "Status field has values constraint");

            // Test methods
            $this->assert(method_exists($order, 'validateCzechICO'), "validateCzechICO static method exists");
            $this->assert(method_exists($order, 'getOrdersByStatus'), "getOrdersByStatus static method exists");
            $this->assert(method_exists($order, 'updateStatus'), "updateStatus method exists");

            // Test ICO validation
            $this->assert(CatalogOrder::validateCzechICO(''), "Empty ICO validation");
            $this->assert(CatalogOrder::validateCzechICO('25596641'), "Valid ICO validation");
            $this->assert(!CatalogOrder::validateCzechICO('12345678'), "Invalid ICO validation");

            echo "✓ CatalogOrder entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "CatalogOrder entity test failed: " . $e->getMessage();
            echo "✗ CatalogOrder entity tests failed\n";
        }
    }

    /**
     * Test CatalogConfig entity
     */
    private function testCatalogConfigEntity()
    {
        echo "Testing CatalogConfig entity...\n";

        try {
            // Test entity creation
            $config = new CatalogConfig();
            $this->assert($config instanceof CatalogConfig, "CatalogConfig entity creation");

            // Test definition
            $this->assert(isset(CatalogConfig::$definition), "CatalogConfig definition exists");
            $this->assert(CatalogConfig::$definition['table'] === 'cig_catalog_config', "CatalogConfig table name");

            // Test static methods
            $this->assert(method_exists('CigCatalog\Entity\CatalogConfig', 'get'), "get static method exists");
            $this->assert(method_exists('CigCatalog\Entity\CatalogConfig', 'set'), "set static method exists");
            $this->assert(method_exists('CigCatalog\Entity\CatalogConfig', 'getDefaultValues'), "getDefaultValues static method exists");

            // Test default values
            $defaults = CatalogConfig::getDefaultValues();
            $this->assert(is_array($defaults), "Default values is array");
            $this->assert(isset($defaults['items_per_page']), "items_per_page default exists");
            $this->assert(isset($defaults['enable_ordering']), "enable_ordering default exists");

            // Test validation
            $this->assert(CatalogConfig::validateValue('items_per_page', 12), "Valid items_per_page validation");
            $this->assert(!CatalogConfig::validateValue('items_per_page', -1), "Invalid items_per_page validation");
            $this->assert(CatalogConfig::validateValue('admin_email', '<EMAIL>'), "Valid email validation");
            $this->assert(!CatalogConfig::validateValue('admin_email', 'invalid-email'), "Invalid email validation");

            echo "✓ CatalogConfig entity tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "CatalogConfig entity test failed: " . $e->getMessage();
            echo "✗ CatalogConfig entity tests failed\n";
        }
    }

    /**
     * Test Czech validator
     */
    private function testCzechValidator()
    {
        echo "Testing CzechValidator...\n";

        try {
            // Test ICO validation
            $this->assert(CzechValidator::validateICO(''), "Empty ICO validation");
            $this->assert(CzechValidator::validateICO('25596641'), "Valid ICO validation");
            $this->assert(!CzechValidator::validateICO('12345678'), "Invalid ICO validation");
            $this->assert(!CzechValidator::validateICO('1234567'), "Short ICO validation");

            // Test DIC validation
            $this->assert(CzechValidator::validateDIC(''), "Empty DIC validation");
            $this->assert(CzechValidator::validateDIC('CZ25596641'), "Valid DIC validation");
            $this->assert(!CzechValidator::validateDIC('SK25596641'), "Invalid DIC country code");

            // Test postal code validation
            $this->assert(CzechValidator::validatePostalCode(''), "Empty postal code validation");
            $this->assert(CzechValidator::validatePostalCode('12345'), "Valid postal code validation");
            $this->assert(CzechValidator::validatePostalCode('123 45'), "Valid postal code with space");
            $this->assert(!CzechValidator::validatePostalCode('1234'), "Invalid short postal code");

            // Test phone validation
            $this->assert(CzechValidator::validatePhoneNumber(''), "Empty phone validation");
            $this->assert(CzechValidator::validatePhoneNumber('+420123456789'), "Valid phone with +420");
            $this->assert(CzechValidator::validatePhoneNumber('123456789'), "Valid domestic phone");
            $this->assert(!CzechValidator::validatePhoneNumber('12345'), "Invalid short phone");

            // Test formatting
            $this->assert(CzechValidator::formatICO('25596641') === '25 596 641', "ICO formatting");
            $this->assert(CzechValidator::formatPostalCode('12345') === '123 45', "Postal code formatting");

            echo "✓ CzechValidator tests passed\n";

        } catch (Exception $e) {
            $this->errors[] = "CzechValidator test failed: " . $e->getMessage();
            echo "✗ CzechValidator tests failed\n";
        }
    }

    /**
     * Assert helper
     */
    private function assert($condition, $message)
    {
        if ($condition) {
            $this->successes[] = $message;
        } else {
            $this->errors[] = $message;
            throw new Exception("Assertion failed: " . $message);
        }
    }

    /**
     * Print test results
     */
    private function printResults()
    {
        echo "\n=== Test Results ===\n";
        echo "Successes: " . count($this->successes) . "\n";
        echo "Errors: " . count($this->errors) . "\n";

        if (!empty($this->errors)) {
            echo "\nErrors:\n";
            foreach ($this->errors as $error) {
                echo "- " . $error . "\n";
            }
        }

        echo "\nTest completed.\n";
    }
}

// Run tests if called directly
if (basename(__FILE__) === basename($_SERVER['SCRIPT_NAME'])) {
    $test = new EntityTest();
    $test->runAllTests();
}
