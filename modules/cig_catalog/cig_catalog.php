<?php
/**
 * CIG Catalog Module for PrestaShop 8.2.0
 * 
 * Advanced catalog management module with ordering system
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @license MIT
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

// Autoload entity classes
require_once __DIR__ . '/src/Entity/Catalog.php';
require_once __DIR__ . '/src/Entity/CatalogOrder.php';
require_once __DIR__ . '/src/Entity/CatalogConfig.php';

// Autoload other classes
require_once __DIR__ . '/src/Validator/CzechValidator.php';
require_once __DIR__ . '/src/Migration/MigrationManager.php';

use PrestaShop\PrestaShop\Core\Module\WidgetInterface;
use Symfony\Component\DependencyInjection\ContainerBuilder;
use Symfony\Component\DependencyInjection\Loader\YamlFileLoader;
use Symfony\Component\Config\FileLocator;
use CigCatalog\Entity\CatalogConfig;
use CigCatalog\Migration\MigrationManager;

class Cig_Catalog extends Module implements WidgetInterface
{
    /**
     * Module configuration
     */
    private const MODULE_VERSION = '1.0.0';
    private const PS_MIN_VERSION = '8.2.0';
    private const PHP_MIN_VERSION = '8.1';
    
    /**
     * Supported hooks
     */
    private const HOOKS = [
        'displayHeader',
        'displayBackOfficeHeader',
        'displayAdminMenuTabLink',
        'actionFrontControllerSetMedia',
        'actionObjectLanguageAddAfter',
        'actionObjectLanguageDeleteAfter',
        'actionObjectLanguageUpdateAfter',
    ];
    
    /**
     * Module constructor
     */
    public function __construct()
    {
        $this->name = 'cig_catalog';
        $this->tab = 'front_office_features';
        $this->version = self::MODULE_VERSION;
        $this->author = 'CIG Development Team';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => self::PS_MIN_VERSION,
            'max' => _PS_VERSION_
        ];
        $this->bootstrap = true;
        
        parent::__construct();
        
        $this->displayName = $this->trans('CIG Catalog', [], 'Modules.Cigcatalog.Admin');
        $this->description = $this->trans(
            'Advanced catalog management module with ordering system and email notifications.',
            [],
            'Modules.Cigcatalog.Admin'
        );
        
        $this->confirmUninstall = $this->trans(
            'Are you sure you want to uninstall this module? All catalog data will be lost.',
            [],
            'Modules.Cigcatalog.Admin'
        );
        
        // Check compatibility
        $this->checkCompatibility();
        
        // Initialize dependency injection
        $this->initializeDependencyInjection();
    }
    
    /**
     * Module installation
     */
    public function install(): bool
    {
        if (!parent::install()) {
            return false;
        }
        
        // Check system requirements
        if (!$this->checkSystemRequirements()) {
            return false;
        }
        
        // Install database tables
        if (!$this->installDatabase()) {
            return false;
        }
        
        // Register hooks
        if (!$this->registerHooks()) {
            return false;
        }
        
        // Create upload directories
        if (!$this->createUploadDirectories()) {
            return false;
        }
        
        // Install admin tabs
        if (!$this->installAdminTabs()) {
            return false;
        }
        
        // Set default configuration
        if (!$this->setDefaultConfiguration()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Module uninstallation
     */
    public function uninstall(): bool
    {
        // Remove database tables
        if (!$this->uninstallDatabase()) {
            return false;
        }
        
        // Remove admin tabs
        if (!$this->uninstallAdminTabs()) {
            return false;
        }
        
        // Remove configuration
        if (!$this->removeConfiguration()) {
            return false;
        }
        
        return parent::uninstall();
    }
    
    /**
     * Check system requirements
     */
    private function checkSystemRequirements(): bool
    {
        // Check PHP version
        if (version_compare(PHP_VERSION, self::PHP_MIN_VERSION, '<')) {
            $this->_errors[] = sprintf(
                $this->trans('PHP %s or higher is required. Current version: %s', [], 'Modules.Cigcatalog.Admin'),
                self::PHP_MIN_VERSION,
                PHP_VERSION
            );
            return false;
        }
        
        // Check required PHP extensions
        $requiredExtensions = ['gd', 'curl', 'zip', 'json'];
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $this->_errors[] = sprintf(
                    $this->trans('PHP extension "%s" is required but not installed.', [], 'Modules.Cigcatalog.Admin'),
                    $extension
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Check PrestaShop compatibility
     */
    private function checkCompatibility(): void
    {
        if (version_compare(_PS_VERSION_, self::PS_MIN_VERSION, '<')) {
            $this->warning = sprintf(
                $this->trans('This module requires PrestaShop %s or higher. Current version: %s', [], 'Modules.Cigcatalog.Admin'),
                self::PS_MIN_VERSION,
                _PS_VERSION_
            );
        }
    }
    
    /**
     * Initialize dependency injection container
     */
    private function initializeDependencyInjection(): void
    {
        $containerBuilder = new ContainerBuilder();
        $loader = new YamlFileLoader($containerBuilder, new FileLocator(__DIR__ . '/config'));
        
        try {
            $loader->load('services.yml');
            $containerBuilder->compile();
            $this->container = $containerBuilder;
        } catch (Exception $e) {
            // Fallback for development - services will be loaded later
            $this->container = null;
        }
    }
    
    /**
     * Register module hooks
     */
    private function registerHooks(): bool
    {
        foreach (self::HOOKS as $hook) {
            if (!$this->registerHook($hook)) {
                $this->_errors[] = sprintf(
                    $this->trans('Failed to register hook: %s', [], 'Modules.Cigcatalog.Admin'),
                    $hook
                );
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Install database tables
     */
    private function installDatabase(): bool
    {
        $sqlFile = __DIR__ . '/sql/install.sql';
        
        if (!file_exists($sqlFile)) {
            $this->_errors[] = $this->trans('Installation SQL file not found.', [], 'Modules.Cigcatalog.Admin');
            return false;
        }
        
        $sql = file_get_contents($sqlFile);
        $sql = str_replace(['PREFIX_', 'ENGINE_TYPE'], [_DB_PREFIX_, _MYSQL_ENGINE_], $sql);
        
        return Db::getInstance()->execute($sql);
    }
    
    /**
     * Uninstall database tables
     */
    private function uninstallDatabase(): bool
    {
        $sqlFile = __DIR__ . '/sql/uninstall.sql';
        
        if (!file_exists($sqlFile)) {
            return true; // No uninstall SQL needed
        }
        
        $sql = file_get_contents($sqlFile);
        $sql = str_replace('PREFIX_', _DB_PREFIX_, $sql);
        
        return Db::getInstance()->execute($sql);
    }
    
    /**
     * Create upload directories
     */
    private function createUploadDirectories(): bool
    {
        $directories = [
            __DIR__ . '/uploads/images/',
            __DIR__ . '/uploads/files/',
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    $this->_errors[] = sprintf(
                        $this->trans('Failed to create directory: %s', [], 'Modules.Cigcatalog.Admin'),
                        $dir
                    );
                    return false;
                }
            }
            
            // Create .htaccess for security
            $htaccessFile = $dir . '.htaccess';
            if (!file_exists($htaccessFile)) {
                $htaccessContent = "# Deny direct access to uploaded files\n";
                $htaccessContent .= "<FilesMatch \"\.php$\">\n";
                $htaccessContent .= "    Order Deny,Allow\n";
                $htaccessContent .= "    Deny from all\n";
                $htaccessContent .= "</FilesMatch>\n";
                
                file_put_contents($htaccessFile, $htaccessContent);
            }
        }
        
        return true;
    }
    
    /**
     * Install admin tabs
     */
    private function installAdminTabs(): bool
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminCatalog';
        $tab->route_name = 'admin_catalog_index';
        $tab->name = [];
        
        foreach (Language::getLanguages(false) as $lang) {
            $tab->name[$lang['id_lang']] = $this->trans('Catalogs', [], 'Modules.Cigcatalog.Admin', $lang['locale']);
        }
        
        $tab->id_parent = (int) Tab::getIdFromClassName('IMPROVE');
        $tab->module = $this->name;
        
        return $tab->add();
    }
    
    /**
     * Uninstall admin tabs
     */
    private function uninstallAdminTabs(): bool
    {
        $idTab = (int) Tab::getIdFromClassName('AdminCatalog');
        
        if ($idTab) {
            $tab = new Tab($idTab);
            return $tab->delete();
        }
        
        return true;
    }
    
    /**
     * Set default configuration
     */
    private function setDefaultConfiguration(): bool
    {
        // Initialize default configuration using CatalogConfig entity
        if (!CatalogConfig::initializeDefaults()) {
            $this->_errors[] = $this->trans('Failed to initialize default configuration.', [], 'Modules.Cigcatalog.Admin');
            return false;
        }

        // Set admin email if not already set
        $adminEmail = CatalogConfig::get('admin_email');
        if (empty($adminEmail)) {
            CatalogConfig::set('admin_email', Configuration::get('PS_SHOP_EMAIL'));
        }

        // Set from email if not already set
        $fromEmail = CatalogConfig::get('from_email');
        if (empty($fromEmail)) {
            CatalogConfig::set('from_email', Configuration::get('PS_SHOP_EMAIL'));
        }

        return true;
    }
    
    /**
     * Remove configuration
     */
    private function removeConfiguration(): bool
    {
        $configKeys = [
            'CIG_CATALOG_ITEMS_PER_PAGE',
            'CIG_CATALOG_ENABLE_ORDERING',
            'CIG_CATALOG_ADMIN_EMAIL',
            'CIG_CATALOG_MAX_FILE_SIZE',
            'CIG_CATALOG_ALLOWED_EXTENSIONS',
        ];
        
        foreach ($configKeys as $key) {
            Configuration::deleteByName($key);
        }
        
        return true;
    }
    
    /**
     * Widget interface methods
     */
    public function getWidgetVariables($hookName, array $configuration): array
    {
        return [];
    }
    
    public function renderWidget($hookName, array $configuration): string
    {
        return '';
    }
}
