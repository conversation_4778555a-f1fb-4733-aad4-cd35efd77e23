# Krok 2: Databázová struktura a entity

## <PERSON><PERSON><PERSON> kroku
Vytvoření databázov<PERSON>ch tabulek a implementace entity tříd s multijazyčnou podporou.

## Co se bude realizovat

### 2.1 SQL skripty pro instalaci

#### Tabulka cig_catalog (hlavn<PERSON> katalogy)
```sql
CREATE TABLE `cig_catalog` (
    `id_catalog` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text,
    `image_path` varchar(500),
    `catalog_url` varchar(500),
    `catalog_file` varchar(500),
    `is_new` tinyint(1) DEFAULT 0,
    `position` int(11) DEFAULT 0,
    `active` tinyint(1) DEFAULT 1,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_catalog`),
    KEY `position` (`position`),
    KEY `active` (`active`),
    KEY `is_new` (`is_new`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_lang (multijazyčnost)
```sql
CREATE TABLE `cig_catalog_lang` (
    `id_catalog` int(11) NOT NULL,
    `id_lang` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `description` text,
    PRIMARY KEY (`id_catalog`, `id_lang`),
    KEY `id_lang` (`id_lang`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_order (objednávky)
```sql
CREATE TABLE `cig_catalog_order` (
    `id_order` int(11) NOT NULL AUTO_INCREMENT,
    `id_catalog` int(11) NOT NULL,
    `company_name` varchar(255),
    `company_id` varchar(50),
    `first_name` varchar(100) NOT NULL,
    `last_name` varchar(100) NOT NULL,
    `email` varchar(255) NOT NULL,
    `phone` varchar(50),
    `address` text NOT NULL,
    `note` text,
    `date_add` datetime NOT NULL,
    PRIMARY KEY (`id_order`),
    KEY `id_catalog` (`id_catalog`),
    KEY `email` (`email`),
    KEY `date_add` (`date_add`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

#### Tabulka cig_catalog_config (konfigurace)
```sql
CREATE TABLE `cig_catalog_config` (
    `id_config` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `value` text,
    `date_add` datetime NOT NULL,
    `date_upd` datetime NOT NULL,
    PRIMARY KEY (`id_config`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2.2 Entity třídy

#### Catalog.php (hlavní entita)
- Rozšíření ObjectModel pro PS kompatibilitu
- Definice všech polí s validací
- Multijazyčná podpora
- Metody pro práci s pozicemi
- Gettery a settery
- Validační pravidla

#### CatalogOrder.php (objednávky)
- Entita pro ukládání objednávek
- Validace emailu, IČO
- Formátování dat
- Metody pro export

#### CatalogConfig.php (konfigurace)
- Správa konfiguračních hodnot
- Statické metody pro rychlý přístup
- Cache mechanismus

### 2.3 Instalační a deinstalační skripty

#### install.sql
- Vytvoření všech tabulek
- Základní indexy pro výkon
- Výchozí konfigurační hodnoty
- Ukázkový katalog

#### uninstall.sql
- Bezpečné odstranění tabulek
- Kontrola závislostí
- Backup možnosti

### 2.4 Migrace a aktualizace
- Verze kontrola
- Postupné migrace
- Rollback mechanismus
- Data integrity kontroly

## Technické detaily

### Databázové optimalizace
- Správné indexy pro rychlé dotazy
- UTF8MB4 pro emoji podporu
- InnoDB engine pro transakce
- Optimalizované datové typy

### Validace
- PrestaShop validátory
- Custom validace pro IČO
- Email format kontrola
- File path validace

### Multijazyčnost
- Automatická detekce jazyků
- Fallback na výchozí jazyk
- Lazy loading překladů

## Výstupy kroku
1. Kompletní SQL skripty
2. Funkční entity třídy
3. Instalační mechanismus
4. Migrace systém
5. Testovací data

## Závislosti
- Krok 1: Základní struktura

## Následující krok
Krok 3: Repository pattern a datová vrstva
