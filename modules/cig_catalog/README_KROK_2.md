# CIG Catalog Module - Krok 2: Databázová struktura a entity

## Přehled realizace

Krok 2 byl úspěšně dokončen. Byly vytvo<PERSON>eny všechny potřebné databázové struktury a entity třídy pro modul CIG Catalog.

## Co bylo implementováno

### 1. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON> (SQL skripty)

#### <PERSON><PERSON><PERSON><PERSON> tabulky:
- **`cig_catalog`** - hlavní tabulka pro katalogy
- **`cig_catalog_lang`** - multijazyčná data katalogů
- **`cig_catalog_order`** - objednávky katalogů
- **`cig_catalog_config`** - konfigurace modulu

#### Klíčové vlastnosti:
- ✅ UTF8MB4 charset pro emoji podporu
- ✅ InnoDB engine pro transakce
- ✅ Optimalizované indexy pro výkon
- ✅ Foreign key constraints pro integritu dat
- ✅ Full-text search indexy

### 2. Entity třídy

#### Catalog.php
- ✅ Rozšíření ObjectModel pro PS kompatibilitu
- ✅ Multijazyčná podpora (name, description, meta_title, atd.)
- ✅ Automatické generování slug
- ✅ Metody pro práci s pozicemi
- ✅ Validační pravidla
- ✅ Statické metody pro dotazy (getActiveCatalogs, getBySlug)

#### CatalogOrder.php
- ✅ Kompletní správa objednávek
- ✅ Validace českého IČO
- ✅ Automatické ukládání IP adresy a user agent
- ✅ Správa stavů objednávek (pending, processed, sent, cancelled)
- ✅ Statistické metody

#### CatalogConfig.php
- ✅ Správa konfiguračních hodnot
- ✅ Cache mechanismus
- ✅ Statické metody pro rychlý přístup
- ✅ Validace konfiguračních hodnot
- ✅ Výchozí hodnoty

### 3. Validátory

#### CzechValidator.php
- ✅ Validace českého IČO (modulo 11 algoritmus)
- ✅ Validace českého DIČ
- ✅ Validace PSČ
- ✅ Validace českých telefonních čísel
- ✅ Formátování pro zobrazení

### 4. Migrace systém

#### MigrationManager.php
- ✅ Postupné migrace databáze
- ✅ Rollback mechanismus
- ✅ Kontrola integrity
- ✅ Verzování změn

### 5. Testování

#### EntityTest.php
- ✅ Testy všech entit
- ✅ Validační testy
- ✅ Funkční testy metod

## Struktura souborů

```
modules/cig_catalog/
├── sql/
│   ├── install.sql          # Kompletní instalační SQL
│   └── uninstall.sql        # Deinstalační SQL
├── src/
│   ├── Entity/
│   │   ├── Catalog.php      # Hlavní entita katalogů
│   │   ├── CatalogOrder.php # Entita objednávek
│   │   └── CatalogConfig.php # Entita konfigurace
│   ├── Validator/
│   │   └── CzechValidator.php # České validátory
│   └── Migration/
│       └── MigrationManager.php # Správa migrací
├── tests/
│   └── EntityTest.php       # Testy entit
└── cig_catalog.php          # Aktualizovaný hlavní soubor
```

## Klíčové funkce

### Multijazyčnost
- Automatická detekce jazyků
- Fallback na výchozí jazyk
- Lazy loading překladů

### Validace
- PrestaShop validátory
- Custom validace pro IČO
- Email format kontrola
- File path validace

### Optimalizace
- Správné indexy pro rychlé dotazy
- Cache mechanismus pro konfiguraci
- Optimalizované datové typy

## Testování

Pro spuštění testů:

```bash
cd modules/cig_catalog/tests/
php EntityTest.php
```

Testy ověřují:
- Správné vytvoření entit
- Funkčnost validátorů
- Definice databázových struktur
- Metody pro práci s daty

## Ukázková data

Při instalaci se automaticky vytvoří:
- Ukázkový katalog v češtině a angličtině
- Výchozí konfigurace
- Potřebné indexy

## Kompatibilita

- ✅ PrestaShop 8.2.0+
- ✅ PHP 8.1+
- ✅ MySQL 5.7+ / MariaDB 10.2+

## Následující kroky

Krok 2 je kompletní a připravený pro krok 3: Repository pattern a datová vrstva.

### Připraveno pro krok 3:
- Databázové struktury
- Entity třídy
- Validátory
- Migrace systém
- Testovací framework

## Poznámky pro vývojáře

### Použití entit:

```php
// Vytvoření nového katalogu
$catalog = new Catalog();
$catalog->name = ['cs' => 'Název', 'en' => 'Name'];
$catalog->active = true;
$catalog->add();

// Načtení aktivních katalogů
$catalogs = Catalog::getActiveCatalogs();

// Práce s konfigurací
CatalogConfig::set('items_per_page', 20);
$itemsPerPage = CatalogConfig::get('items_per_page', 12);

// Validace IČO
$isValid = CzechValidator::validateICO('25596641');
```

### Databázové dotazy:
Všechny entity používají PrestaShop ObjectModel pattern, což zajišťuje:
- Automatické escapování SQL
- Validaci dat
- Cache mechanismus
- Multijazyčnou podporu

## Bezpečnost

- ✅ SQL injection ochrana
- ✅ XSS ochrana
- ✅ Validace všech vstupů
- ✅ Sanitizace dat
- ✅ Foreign key constraints
